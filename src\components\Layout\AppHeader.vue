<template>
  <div class="app-header">
    <!-- 左侧：当前对话标题 -->
    <div class="header-left">
      <div class="conversation-info">
        <h2 class="conversation-title">
          {{ currentConversation?.title || '新对话' }}
        </h2>
        <div class="conversation-meta">
          <span v-if="messageCount > 0" class="message-count">
            {{ messageCount }} 条消息
          </span>
        </div>
      </div>
    </div>
    
    <!-- 中间：搜索框 -->
    <div class="header-center">
      <n-input
        v-model:value="searchQuery"
        placeholder="搜索对话内容..."
        clearable
        class="search-input"
        @keyup.enter="handleSearch"
      >
        <template #prefix>
          <n-icon :component="SearchIcon" />
        </template>
      </n-input>
    </div>
    
    <!-- 右侧：操作按钮 -->
    <div class="header-right">
      <!-- 模型选择 -->
      <n-popover
        v-model:show="showModelSelector"
        trigger="click"
        placement="bottom-end"
        :show-arrow="false"
        raw
        class="model-selector-popover"
      >
        <template #trigger>
          <div class="model-selector">
            <div class="model-selector-content">
              <span class="model-icon">{{ currentModel?.icon || '🤖' }}</span>
              <span class="model-name">{{ currentModel?.displayName || '选择模型' }}</span>
              <n-icon class="dropdown-arrow" :component="ChevronDownIcon" />
            </div>
          </div>
        </template>

        <div class="model-selector-panel">
          <!-- 搜索框 -->
          <div class="model-search">
            <n-input
              v-model:value="modelSearchQuery"
              placeholder="搜索模型名称..."
              size="small"
              clearable
            >
              <template #prefix>
                <n-icon :component="SearchIcon" />
              </template>
            </n-input>
          </div>

          <!-- 提供商标签 -->
          <div class="provider-tags">
            <div
              v-for="provider in availableProviders"
              :key="provider.id"
              class="provider-tag"
              :class="{ 'provider-tag--active': selectedProviderId === provider.id }"
              @click="selectProvider(provider.id)"
            >
              <span v-if="provider.id === 'favorites'" class="tag-icon">⭐</span>
              {{ provider.displayName }}
            </div>
          </div>

          <!-- 模型列表 -->
          <div class="model-list">
            <div
              v-for="model in filteredModels"
              :key="model.id"
              class="model-option"
              :class="{ 'model-option--active': currentModel?.id === model.id }"
              @click="handleModelSelect(model.id)"
            >
              <span class="model-option-icon">{{ model.icon }}</span>
              <div class="model-option-info">
                <div class="model-option-name">{{ model.displayName }}</div>
              </div>
              <div
                class="model-favorite-btn"
                :class="{ 'model-favorite-btn--active': favoriteModelIds.has(model.id) }"
                @click.stop="toggleModelFavorite(model.id, model.providerId)"
              >
                <n-icon
                  :component="StarIcon"
                  :class="{ 'star-filled': favoriteModelIds.has(model.id) }"
                />
              </div>
            </div>
          </div>
        </div>
      </n-popover>
      
      <!-- 模型参数设置 -->
      <n-popover
        v-model:show="showModelParams"
        trigger="click"
        placement="bottom-end"
        :show-arrow="false"
        raw
        class="model-params-popover"
      >
        <template #trigger>
          <n-button quaternary circle>
            <template #icon>
              <n-icon :component="SlidersIcon" />
            </template>
          </n-button>
        </template>

        <div class="model-params-panel">
          <div class="params-header">
            <h3>模型参数设置</h3>
          </div>

          <div class="params-content">
            <!-- 系统提示词 -->
            <div class="param-group">
              <div class="param-label-row">
                <label class="param-label">系统提示词</label>
                <n-button
                  size="tiny"
                  text
                  @click="showSystemPromptModal = true"
                  class="edit-prompt-btn"
                >
                  编辑
                </n-button>
              </div>
              <div class="prompt-preview">
                {{ systemPrompt || '点击编辑设置系统提示词...' }}
              </div>
            </div>

            <!-- 温度 -->
            <div class="param-group">
              <label class="param-label">温度 ({{ temperature }})</label>
              <n-slider
                v-model:value="temperature"
                :min="0"
                :max="2"
                :step="0.1"
                @update:value="updateChatSettings"
              />
              <div class="param-desc">控制回答的随机性，越高越有创意</div>
            </div>

            <!-- 最大Token数 -->
            <div class="param-group">
              <label class="param-label">最大Token数</label>
              <n-input-number
                v-model:value="maxTokens"
                :min="1"
                :max="4096"
                size="small"
                @blur="updateChatSettings"
              />
              <div class="param-desc">限制回答的最大长度</div>
            </div>

            <!-- Top P -->
            <div class="param-group">
              <label class="param-label">Top P ({{ topP }})</label>
              <n-slider
                v-model:value="topP"
                :min="0"
                :max="1"
                :step="0.1"
                @update:value="updateChatSettings"
              />
              <div class="param-desc">控制词汇选择的多样性</div>
            </div>

            <!-- 重复惩罚 -->
            <div class="param-group">
              <label class="param-label">重复惩罚 ({{ frequencyPenalty }})</label>
              <n-slider
                v-model:value="frequencyPenalty"
                :min="0"
                :max="2"
                :step="0.1"
                @update:value="updateChatSettings"
              />
              <div class="param-desc">减少重复内容的出现</div>
            </div>

            <!-- 重置按钮 -->
            <div class="params-actions">
              <n-button size="small" @click="resetToDefaults">
                重置默认值
              </n-button>
            </div>
          </div>
        </div>
      </n-popover>
      
      <!-- 设置 -->
      <n-button quaternary circle @click="openSettings">
        <template #icon>
          <n-icon :component="SettingsIcon" />
        </template>
      </n-button>
      
      <!-- 主题切换 -->
      <n-button quaternary circle @click="toggleTheme">
        <template #icon>
          <n-icon :component="themeIcon" />
        </template>
      </n-button>
      

    </div>

    <!-- 系统提示词编辑模态框 -->
    <n-modal
      v-model:show="showSystemPromptModal"
      preset="card"
      title="编辑系统提示词"
      class="system-prompt-modal"
      :style="{ width: '600px' }"
    >
      <div class="prompt-editor">
        <n-input
          v-model:value="systemPrompt"
          type="textarea"
          placeholder="设置AI的角色和行为，例如：你是一个专业的编程助手，擅长解答技术问题..."
          :rows="8"
          show-count
          :maxlength="2000"
          @input="updateChatSettings"
        />

        <div class="prompt-templates">
          <div class="templates-header">
            <h4>常用模板：</h4>
            <n-button
              size="tiny"
              type="primary"
              @click="showAddTemplateModal = true"
              class="add-template-btn"
            >
              + 添加
            </n-button>
          </div>
          <div class="template-tags">
            <div
              v-for="template in promptTemplates"
              :key="template.id"
              class="template-tag"
              @click="applyTemplate(template.content)"
            >
              <span class="template-name">{{ template.name }}</span>
              <span
                v-if="!template.isBuiltIn"
                class="template-delete"
                @click.stop="deleteTemplate(template.id)"
              >
                ×
              </span>
            </div>
          </div>
        </div>
      </div>
    </n-modal>

    <!-- 添加模板模态框 -->
    <n-modal
      v-model:show="showAddTemplateModal"
      preset="card"
      title="添加自定义模板"
      class="add-template-modal"
      :style="{ width: '500px' }"
    >
      <div class="add-template-form">
        <div class="form-group">
          <label class="form-label">模板名称</label>
          <n-input
            v-model:value="newTemplateName"
            placeholder="请输入模板名称"
            maxlength="20"
            show-count
          />
        </div>

        <div class="form-group">
          <label class="form-label">模板内容</label>
          <n-input
            v-model:value="newTemplateContent"
            type="textarea"
            placeholder="请输入提示词内容"
            :rows="6"
            maxlength="2000"
            show-count
          />
        </div>

        <div class="form-actions">
          <n-button @click="cancelAddTemplate">取消</n-button>
          <n-button
            type="primary"
            @click="saveNewTemplate"
            :disabled="!newTemplateName.trim() || !newTemplateContent.trim()"
          >
            保存
          </n-button>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import {
  Search as SearchIcon,
  Settings as SettingsIcon,
  Brain as BrainIcon,
  Bot as RobotIcon,
  Sun as SunIcon,
  Moon as MoonIcon,
  ChevronDown as ChevronDownIcon,
  Star as StarIcon,
  Sliders as SlidersIcon
} from 'lucide-vue-next'
import { useChatStore } from '@/stores/chat'
import { useSettingsStore } from '@/stores/settings'

const router = useRouter()
const message = useMessage()
const chatStore = useChatStore()
const settingsStore = useSettingsStore()

const searchQuery = ref('')
const modelSearchQuery = ref('')
const selectedProviderId = ref('all')
const showModelSelector = ref(false)
const showModelParams = ref(false)
const showSystemPromptModal = ref(false)
const showAddTemplateModal = ref(false)

// 模型参数
const systemPrompt = ref('')
const temperature = ref(0.7)
const maxTokens = ref(2048)
const topP = ref(1.0)
const frequencyPenalty = ref(0)

// 新模板表单
const newTemplateName = ref('')
const newTemplateContent = ref('')

// 提示词模板（响应式）
const promptTemplates = ref([
  {
    id: '1',
    name: '通用助手',
    content: '你是一个有用的AI助手，请用中文回答问题。',
    isBuiltIn: true
  },
  {
    id: '2',
    name: '编程助手',
    content: '你是一个专业的编程助手，擅长多种编程语言和技术栈。请提供准确、实用的代码建议和解决方案。',
    isBuiltIn: true
  },
  {
    id: '3',
    name: '写作助手',
    content: '你是一个专业的写作助手，擅长各种文体的写作，包括技术文档、创意写作、学术论文等。请提供高质量的写作建议。',
    isBuiltIn: true
  },
  {
    id: '4',
    name: '翻译助手',
    content: '你是一个专业的翻译助手，能够准确翻译多种语言。请保持原文的语调和含义，提供自然流畅的翻译。',
    isBuiltIn: true
  },
  {
    id: '5',
    name: '学习导师',
    content: '你是一个耐心的学习导师，善于解释复杂概念，能够根据学习者的水平调整解释方式。请用简单易懂的语言回答问题。',
    isBuiltIn: true
  }
])

// 计算属性
const currentConversation = computed(() => chatStore.currentConversation)
const messageCount = computed(() => chatStore.messages.length)

const currentModel = computed(() => {
  const modelId = currentConversation.value?.model || 'gpt-3.5-turbo'
  for (const provider of settingsStore.providers) {
    const model = provider.models.find(m => m.id === modelId)
    if (model) {
      return {
        ...model,
        icon: provider.icon || '🤖'
      }
    }
  }
  return null
})

const themeIcon = computed(() => {
  return settingsStore.app.theme === 'dark' ? SunIcon : MoonIcon
})

// 可用的提供商列表
const availableProviders = computed(() => {
  const providers = settingsStore.providers.filter(p => p.enabled && p.models.length > 0)
  return [
    { id: 'all', displayName: '全部' },
    { id: 'favorites', displayName: '收藏' },
    ...providers.map(p => ({ id: p.id, displayName: p.displayName }))
  ]
})

// 所有可用模型（带提供商信息）
const allModels = computed(() => {
  const models: any[] = []
  settingsStore.providers.forEach(provider => {
    if (provider.enabled) {
      // 获取用户选中的模型ID列表，如果没有则显示所有模型
      const selectedModelIds = provider.selectedModels || provider.models.map(m => m.id)

      provider.models.forEach(model => {
        // 只显示用户选中的模型
        if (selectedModelIds.includes(model.id)) {
          models.push({
            ...model,
            icon: provider.icon || '🤖',
            providerId: provider.id,
            providerName: provider.displayName
          })
        }
      })
    }
  })
  return models
})

// 获取所有收藏的模型ID
const favoriteModelIds = computed(() => {
  const favorites = new Set<string>()
  settingsStore.providers.forEach(provider => {
    if (provider.favoriteModels) {
      provider.favoriteModels.forEach(modelId => favorites.add(modelId))
    }
  })
  return favorites
})

// 过滤后的模型列表
const filteredModels = computed(() => {
  let models = allModels.value

  // 按提供商过滤
  if (selectedProviderId.value === 'favorites') {
    // 只显示收藏的模型
    models = models.filter(m => favoriteModelIds.value.has(m.id))
  } else if (selectedProviderId.value !== 'all') {
    models = models.filter(m => m.providerId === selectedProviderId.value)
  }

  // 按搜索关键词过滤
  if (modelSearchQuery.value.trim()) {
    const query = modelSearchQuery.value.toLowerCase()
    models = models.filter(m =>
      m.displayName.toLowerCase().includes(query) ||
      m.providerName.toLowerCase().includes(query)
    )
  }

  return models
})



// 方法
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    // 实现搜索功能
    console.log('搜索:', searchQuery.value)
  }
}

const selectProvider = (providerId: string) => {
  selectedProviderId.value = providerId
}

const handleModelSelect = (modelId: string) => {
  if (currentConversation.value) {
    // 更新对话的模型
    chatStore.updateConversation(currentConversation.value.id, { model: modelId })

    // 同时更新currentConversation的引用，确保UI立即更新
    currentConversation.value.model = modelId
  }
  // 选择模型后关闭popover
  showModelSelector.value = false
}

const toggleModelFavorite = (modelId: string, providerId: string) => {
  const provider = settingsStore.providers.find(p => p.id === providerId)
  if (!provider) return

  const favorites = provider.favoriteModels || []
  const isFavorite = favorites.includes(modelId)

  const updatedFavorites = isFavorite
    ? favorites.filter(id => id !== modelId)
    : [...favorites, modelId]

  settingsStore.updateProvider(providerId, { favoriteModels: updatedFavorites })
}

// 模型参数相关方法
const updateChatSettings = () => {
  settingsStore.updateChatSettings({
    systemPrompt: systemPrompt.value,
    temperature: temperature.value,
    maxTokens: maxTokens.value,
    topP: topP.value,
    frequencyPenalty: frequencyPenalty.value
  })
}

const resetToDefaults = () => {
  systemPrompt.value = '你是一个有用的AI助手，请用中文回答问题。'
  temperature.value = 0.7
  maxTokens.value = 2048
  topP.value = 1.0
  frequencyPenalty.value = 0
  updateChatSettings()
}

const loadChatSettings = () => {
  const settings = settingsStore.chat
  systemPrompt.value = settings.systemPrompt || '你是一个有用的AI助手，请用中文回答问题。'
  temperature.value = settings.temperature
  maxTokens.value = settings.maxTokens
  topP.value = settings.topP
  frequencyPenalty.value = settings.frequencyPenalty
}

const applyTemplate = (content: string) => {
  systemPrompt.value = content
  updateChatSettings()
}

const deleteTemplate = (templateId: string) => {
  const index = promptTemplates.value.findIndex(t => t.id === templateId)
  if (index > -1) {
    promptTemplates.value.splice(index, 1)
    saveTemplates()
  }
}

const saveNewTemplate = () => {
  if (!newTemplateName.value.trim() || !newTemplateContent.value.trim()) return

  const newTemplate = {
    id: Date.now().toString(),
    name: newTemplateName.value.trim(),
    content: newTemplateContent.value.trim(),
    isBuiltIn: false
  }

  promptTemplates.value.push(newTemplate)
  saveTemplates()

  // 清空表单
  newTemplateName.value = ''
  newTemplateContent.value = ''
  showAddTemplateModal.value = false
}

const cancelAddTemplate = () => {
  newTemplateName.value = ''
  newTemplateContent.value = ''
  showAddTemplateModal.value = false
}

const saveTemplates = () => {
  // 只保存自定义模板
  const customTemplates = promptTemplates.value.filter(t => !t.isBuiltIn)
  localStorage.setItem('custom-prompt-templates', JSON.stringify(customTemplates))
}

const loadTemplates = () => {
  try {
    const stored = localStorage.getItem('custom-prompt-templates')
    if (stored) {
      const customTemplates = JSON.parse(stored)
      // 合并内置模板和自定义模板
      promptTemplates.value = [
        ...promptTemplates.value.filter(t => t.isBuiltIn),
        ...customTemplates
      ]
    }
  } catch (error) {
    console.error('加载自定义模板失败:', error)
  }
}

const openSettings = () => {
  // 使用事件总线通知主应用打开设置侧边栏
  const event = new CustomEvent('open-settings-sidebar')
  window.dispatchEvent(event)
}

const handleOpenSystemPromptEditor = () => {
  showSystemPromptModal.value = true
}

// 组件挂载时加载设置
onMounted(() => {
  loadChatSettings()
  loadTemplates()
  window.addEventListener('open-system-prompt-editor', handleOpenSystemPromptEditor)
})

onUnmounted(() => {
  window.removeEventListener('open-system-prompt-editor', handleOpenSystemPromptEditor)
})

const toggleTheme = () => {
  const currentTheme = settingsStore.app.theme
  const newTheme = currentTheme === 'dark' ? 'light' : 'dark'
  settingsStore.updateAppSettings({ theme: newTheme })
}

// 窗口控制
const minimizeWindow = async () => {
  try {
    const { appWindow } = await import('@tauri-apps/api/window')
    await appWindow.minimize()
  } catch (error) {
    console.log('最小化窗口 (浏览器模式)')
  }
}

const maximizeWindow = async () => {
  try {
    const { appWindow } = await import('@tauri-apps/api/window')
    const isMaximized = await appWindow.isMaximized()
    if (isMaximized) {
      await appWindow.unmaximize()
    } else {
      await appWindow.maximize()
    }
  } catch (error) {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      document.documentElement.requestFullscreen()
    }
  }
}

const closeWindow = async () => {
  try {
    const { appWindow } = await import('@tauri-apps/api/window')
    await appWindow.close()
  } catch (error) {
    if (confirm('确定要关闭应用吗？')) {
      window.close()
    }
  }
}
</script>

<style scoped>
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0 16px;
  background: #ffffff;
}

.header-left {
  flex: 1;
  min-width: 0;
}

.conversation-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.conversation-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.conversation-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: #6b7280;
}

.model-badge {
  background: #e5e7eb;
  color: #374151;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.message-count {
  color: #9ca3af;
}

.header-center {
  flex: 0 0 280px;
  margin: 0 16px;
}

.search-input {
  width: 100%;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 模型选择器样式 */
.model-selector {
  cursor: pointer;
  user-select: none;
}

.model-selector-content {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  transition: all 0.2s ease;
  min-width: 120px;
}

.model-selector-content:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.model-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.model-name {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-arrow {
  font-size: 14px;
  color: #6b7280;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.model-selector:hover .dropdown-arrow {
  color: #3b82f6;
}

/* 模型选择面板样式 */
.model-selector-panel {
  width: 320px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.model-search {
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.provider-tags {
  display: flex;
  gap: 4px;
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
  flex-wrap: wrap;
}

.provider-tag {
  padding: 4px 12px;
  border-radius: 16px;
  background: #f3f4f6;
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.provider-tag:hover {
  background: #e5e7eb;
}

.provider-tag--active {
  background: #3b82f6;
  color: white;
}

.tag-icon {
  margin-right: 4px;
}

.model-list {
  max-height: 163px;
  overflow-y: auto;
}

.model-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.model-option:hover {
  background: #f9fafb;
}

.model-option--active {
  background: #eff6ff;
  color: #2563eb;
}

.model-option-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.model-option-info {
  flex: 1;
  min-width: 0;
}

.model-option-name {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.model-favorite-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: #d1d5db;
  cursor: pointer;
  border-radius: 3px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.model-favorite-btn:hover {
  color: #fbbf24;
  background: #fef3c7;
}

.model-favorite-btn--active {
  color: #f59e0b;
}

.model-favorite-btn--active .star-filled {
  fill: currentColor;
}

.model-favorite-btn:not(.model-favorite-btn--active) .star-filled {
  fill: none;
}

/* 模型参数面板样式 */
.model-params-panel {
  width: 320px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.params-header {
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  background: #f9fafb;
}

.params-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.params-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.param-group {
  margin-bottom: 16px;
}

.param-group:last-child {
  margin-bottom: 0;
}

.param-label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.param-label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.edit-prompt-btn {
  font-size: 11px;
  color: #3b82f6;
}

.prompt-preview {
  font-size: 12px;
  color: #6b7280;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 8px;
  min-height: 40px;
  max-height: 80px;
  line-height: 1.4;
  cursor: pointer;
  transition: border-color 0.2s ease;
  overflow-y: auto;
  word-wrap: break-word;
}

.prompt-preview:hover {
  border-color: #3b82f6;
}

.param-desc {
  font-size: 11px;
  color: #9ca3af;
  margin-top: 4px;
}

.params-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
  display: flex;
  justify-content: center;
}

/* 系统提示词模态框样式 */
.prompt-editor {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.templates-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.templates-header h4 {
  margin: 0;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.add-template-btn {
  font-size: 11px;
}

.template-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  max-height: 80px;
  overflow-y: auto;
}

.template-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  font-size: 11px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.template-tag:hover {
  background: #e5e7eb;
  border-color: #3b82f6;
}

.template-name {
  font-weight: 500;
}

.template-delete {
  font-size: 14px;
  color: #9ca3af;
  margin-left: 2px;
  transition: color 0.2s ease;
}

.template-delete:hover {
  color: #ef4444;
}

/* 添加模板模态框样式 */
.add-template-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-label {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-center {
    flex: 0 0 180px;
    margin: 0 8px;
  }
  
  .conversation-title {
    max-width: 150px;
    font-size: 14px;
  }
  
  .desktop-only {
    display: none;
  }
  
  .header-right {
    gap: 4px;
  }
}

@media (max-width: 640px) {
  .header-center {
    display: none;
  }
  
  .conversation-meta {
    display: none;
  }
  
  .conversation-title {
    max-width: 120px;
  }
}
</style>
