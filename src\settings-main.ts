import { createApp } from 'vue'
import { createP<PERSON> } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import naive from 'naive-ui'
import SettingsApp from './SettingsApp.vue'

// 导入样式
import 'uno.css'
import './styles/main.css'

// 通用字体
const meta = document.createElement('meta')
meta.name = 'naive-ui-style'
document.head.appendChild(meta)

// 设置窗口专用路由配置
const routes = [
  {
    path: '/',
    name: 'Settings',
    component: () => import('./views/SettingsWindow.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const pinia = createPinia()
const app = createApp(SettingsApp)

app.use(pinia)
app.use(router)
app.use(naive)

app.mount('#app')
