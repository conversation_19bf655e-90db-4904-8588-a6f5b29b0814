<template>
  <div class="settings-window">
    <!-- 窗口标题栏 -->
    <div class="window-titlebar" data-tauri-drag-region>
      <div class="titlebar-content">
        <div class="titlebar-icon">
          <i class="i-carbon-settings text-lg" />
        </div>
        <div class="titlebar-title">AI桌面客户端 - 设置</div>
        <div class="titlebar-controls">
          <button 
            class="titlebar-button minimize"
            @click="minimizeWindow"
            title="最小化"
          >
            <i class="i-carbon-minimize" />
          </button>
          <button 
            class="titlebar-button close"
            @click="closeWindow"
            title="关闭"
          >
            <i class="i-carbon-close" />
          </button>
        </div>
      </div>
    </div>

    <!-- 设置内容区域 -->
    <div class="settings-content">
      <div class="settings-layout">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1>设置</h1>
          <p>配置和管理AI服务提供商</p>
        </div>

        <!-- 顶部导航标签 -->
        <div class="settings-tabs">
          <div
            v-for="tab in settingsTabs"
            :key="tab.key"
            class="tab-item"
            :class="{ active: activeTab === tab.key }"
            @click="activeTab = tab.key"
          >
            <i :class="tab.icon" />
            <span>{{ tab.label }}</span>
          </div>
        </div>

        <!-- 设置内容 -->
        <div class="settings-panel">
          <div class="panel-content">
            <!-- 通用设置 -->
            <div v-if="activeTab === 'general'" class="setting-group">
              <GeneralSettings />
            </div>

            <!-- AI提供商设置 -->
            <div v-else-if="activeTab === 'providers'" class="setting-group">
              <AIProviderSettings />
            </div>

            <!-- 模型参数设置 -->
            <div v-else-if="activeTab === 'models'" class="setting-group">
              <ModelSettings />
            </div>

            <!-- 助手管理 -->
            <div v-else-if="activeTab === 'assistants'" class="setting-group">
              <AssistantSettings />
            </div>

            <!-- 高级设置 -->
            <div v-else-if="activeTab === 'advanced'" class="setting-group">
              <AdvancedSettings />
            </div>

            <!-- 关于 -->
            <div v-else-if="activeTab === 'about'" class="setting-group">
              <AboutSettings />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="settings-footer">
      <div class="footer-actions">
        <n-button @click="resetAllSettings" quaternary>
          <template #icon>
            <i class="i-carbon-reset" />
          </template>
          重置所有设置
        </n-button>
        
        <div class="footer-right">
          <n-button @click="closeWindow" secondary>
            取消
          </n-button>
          <n-button @click="saveAndClose" type="primary">
            <template #icon>
              <i class="i-carbon-checkmark" />
            </template>
            保存并关闭
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMessage, darkTheme, type GlobalThemeOverrides } from 'naive-ui'
import { useSettingsStore } from '@/stores/settings'

// 导入设置组件
import AIProviderSettings from '@/components/Settings/AIProviderSettings.vue'
import GeneralSettings from '@/components/Settings/GeneralSettings.vue'
import ModelSettings from '@/components/Settings/ModelSettings.vue'
import AssistantSettings from '@/components/Settings/AssistantSettings.vue'
import AdvancedSettings from '@/components/Settings/AdvancedSettings.vue'
import AboutSettings from '@/components/Settings/AboutSettings.vue'

const message = useMessage()
const settingsStore = useSettingsStore()

// 检查是否在Tauri环境中
const isTauri = typeof window !== 'undefined' && '__TAURI__' in window
let currentWindow: any = null

// 动态获取Tauri窗口API
const getTauriWindow = async () => {
  if (!isTauri) return null
  try {
    const { getCurrentWebviewWindow } = await import('@tauri-apps/api/webviewWindow')
    return getCurrentWebviewWindow()
  } catch (error) {
    console.warn('Tauri API不可用')
    return null
  }
}

// 当前激活的标签页
const activeTab = ref('providers')

// 设置标签页配置
const settingsTabs = [
  {
    key: 'general',
    label: '通用',
    icon: 'i-carbon-settings',
    description: '应用外观、语言和基本行为设置'
  },
  {
    key: 'providers',
    label: 'AI提供商',
    icon: 'i-carbon-cloud-service-management',
    description: '配置和管理AI服务提供商'
  },
  {
    key: 'models',
    label: '模型参数',
    icon: 'i-carbon-model-alt',
    description: '调整AI模型的参数和行为'
  },
  {
    key: 'assistants',
    label: '助手管理',
    icon: 'i-carbon-user-multiple',
    description: '创建和管理AI助手'
  },
  {
    key: 'advanced',
    label: '高级设置',
    icon: 'i-carbon-settings-adjust',
    description: '高级功能和开发者选项'
  },
  {
    key: 'about',
    label: '关于',
    icon: 'i-carbon-information',
    description: '应用信息和版本详情'
  }
]

// 计算属性
const theme = computed(() => settingsStore.app.theme === 'dark' ? darkTheme : null)
const currentTabInfo = computed(() => settingsTabs.find(tab => tab.key === activeTab.value))

// 主题覆盖
const themeOverrides: GlobalThemeOverrides = {
  common: {
    primaryColor: '#3b82f6',
    primaryColorHover: '#2563eb',
    primaryColorPressed: '#1d4ed8',
    borderRadius: '8px'
  }
}

// 窗口操作方法
const minimizeWindow = async () => {
  try {
    const window = await getTauriWindow()
    if (window) {
      await window.minimize()
    } else {
      console.log('最小化窗口 (浏览器环境)')
    }
  } catch (error) {
    console.error('最小化窗口失败:', error)
  }
}

const closeWindow = async () => {
  try {
    const window = await getTauriWindow()
    if (window) {
      await window.close()
    } else {
      // 在浏览器环境中关闭窗口
      if (window.opener) {
        window.close()
      } else {
        console.log('关闭窗口 (浏览器环境)')
      }
    }
  } catch (error) {
    console.error('关闭窗口失败:', error)
  }
}

const saveAndClose = async () => {
  try {
    // 保存所有设置
    settingsStore.saveSettings()
    message.success('设置已保存')
    
    // 延迟关闭窗口，让用户看到成功消息
    setTimeout(async () => {
      await closeWindow()
    }, 1000)
  } catch (error) {
    message.error('保存设置失败')
    console.error('保存设置失败:', error)
  }
}

const resetAllSettings = () => {
  message.warning('重置功能开发中...')
}

// 组件挂载时的初始化
onMounted(async () => {
  try {
    const window = await getTauriWindow()
    if (window) {
      currentWindow = window
      // 设置窗口最小尺寸
      await currentWindow.setMinSize({ width: 800, height: 600 })

      // 监听窗口关闭事件
      currentWindow.listen('tauri://close-requested', () => {
        // 可以在这里添加关闭前的确认逻辑
      })
    } else {
      console.log('在浏览器环境中运行设置窗口')
    }
  } catch (error) {
    console.error('初始化窗口失败:', error)
  }
})
</script>

<style scoped>
.settings-window {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  overflow: hidden;
}

/* 窗口标题栏 */
.window-titlebar {
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  user-select: none;
}

.titlebar-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  color: white;
}

.titlebar-icon {
  display: flex;
  align-items: center;
}

.titlebar-title {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
  text-align: center;
  margin: 0 20px;
}

.titlebar-controls {
  display: flex;
  gap: 8px;
}

.titlebar-button {
  width: 28px;
  height: 28px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.titlebar-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.titlebar-button.close:hover {
  background: #ef4444;
}

/* 设置内容区域 */
.settings-content {
  flex: 1;
  overflow: hidden;
  background: #f8fafc;
}

.settings-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 页面标题 */
.page-header {
  padding: 32px 0 24px;
  text-align: center;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-header p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

/* 顶部标签导航 */
.settings-tabs {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  overflow-x: auto;
  gap: 4px;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  min-width: fit-content;
}

.tab-item:hover {
  background: #f1f5f9;
  color: #374151;
}

.tab-item.active {
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.tab-item i {
  font-size: 16px;
}

/* 设置面板 */
.settings-panel {
  flex: 1;
  overflow: hidden;
}

.panel-content {
  height: 100%;
  overflow-y: auto;
  padding-bottom: 24px;
}

.setting-group {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 底部操作栏 */
.settings-footer {
  height: 72px;
  background: white;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  padding: 0 32px;
}

.footer-actions {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-right {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .settings-layout {
    padding: 0 16px;
  }

  .page-header {
    padding: 24px 0 16px;
  }

  .page-header h1 {
    font-size: 28px;
  }

  .settings-tabs {
    padding: 6px;
    margin-bottom: 16px;
  }

  .tab-item {
    padding: 10px 16px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .settings-layout {
    padding: 0 12px;
  }

  .page-header {
    padding: 16px 0 12px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .settings-tabs {
    flex-direction: column;
    gap: 2px;
  }

  .tab-item {
    justify-content: center;
    padding: 12px 16px;
  }

  .panel-content {
    padding-bottom: 16px;
  }
}
</style>
