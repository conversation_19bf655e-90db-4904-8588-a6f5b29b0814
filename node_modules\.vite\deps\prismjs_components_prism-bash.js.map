{"version": 3, "sources": ["../../prismjs/components/prism-bash.js"], "sourcesContent": ["(function (Prism) {\n\t// $ set | grep '^[A-Z][^[:space:]]*=' | cut -d= -f1 | tr '\\n' '|'\n\t// + LC_ALL, RANDOM, REPLY, SECONDS.\n\t// + make sure PS1..4 are here as they are not always set,\n\t// - some useless things.\n\tvar envVars = '\\\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\\\b';\n\n\tvar commandAfterHeredoc = {\n\t\tpattern: /(^([\"']?)\\w+\\2)[ \\t]+\\S.*/,\n\t\tlookbehind: true,\n\t\talias: 'punctuation', // this looks reasonably well in all themes\n\t\tinside: null // see below\n\t};\n\n\tvar insideString = {\n\t\t'bash': commandAfterHeredoc,\n\t\t'environment': {\n\t\t\tpattern: RegExp('\\\\$' + envVars),\n\t\t\talias: 'constant'\n\t\t},\n\t\t'variable': [\n\t\t\t// [0]: Arithmetic Environment\n\t\t\t{\n\t\t\t\tpattern: /\\$?\\(\\([\\s\\S]+?\\)\\)/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t// If there is a $ sign at the beginning highlight $(( and )) as variable\n\t\t\t\t\t'variable': [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpattern: /(^\\$\\(\\([\\s\\S]+)\\)\\)/,\n\t\t\t\t\t\t\tlookbehind: true\n\t\t\t\t\t\t},\n\t\t\t\t\t\t/^\\$\\(\\(/\n\t\t\t\t\t],\n\t\t\t\t\t'number': /\\b0x[\\dA-Fa-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee]-?\\d+)?/,\n\t\t\t\t\t// Operators according to https://www.gnu.org/software/bash/manual/bashref.html#Shell-Arithmetic\n\t\t\t\t\t'operator': /--|\\+\\+|\\*\\*=?|<<=?|>>=?|&&|\\|\\||[=!+\\-*/%<>^&|]=?|[?~:]/,\n\t\t\t\t\t// If there is no $ sign at the beginning highlight (( and )) as punctuation\n\t\t\t\t\t'punctuation': /\\(\\(?|\\)\\)?|,|;/\n\t\t\t\t}\n\t\t\t},\n\t\t\t// [1]: Command Substitution\n\t\t\t{\n\t\t\t\tpattern: /\\$\\((?:\\([^)]+\\)|[^()])+\\)|`[^`]+`/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'variable': /^\\$\\(|^`|\\)$|`$/\n\t\t\t\t}\n\t\t\t},\n\t\t\t// [2]: Brace expansion\n\t\t\t{\n\t\t\t\tpattern: /\\$\\{[^}]+\\}/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'operator': /:[-=?+]?|[!\\/]|##?|%%?|\\^\\^?|,,?/,\n\t\t\t\t\t'punctuation': /[\\[\\]]/,\n\t\t\t\t\t'environment': {\n\t\t\t\t\t\tpattern: RegExp('(\\\\{)' + envVars),\n\t\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t\talias: 'constant'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t/\\$(?:\\w+|[#?*!@$])/\n\t\t],\n\t\t// Escape sequences from echo and printf's manuals, and escaped quotes.\n\t\t'entity': /\\\\(?:[abceEfnrtv\\\\\"]|O?[0-7]{1,3}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|x[0-9a-fA-F]{1,2})/\n\t};\n\n\tPrism.languages.bash = {\n\t\t'shebang': {\n\t\t\tpattern: /^#!\\s*\\/.*/,\n\t\t\talias: 'important'\n\t\t},\n\t\t'comment': {\n\t\t\tpattern: /(^|[^\"{\\\\$])#.*/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'function-name': [\n\t\t\t// a) function foo {\n\t\t\t// b) foo() {\n\t\t\t// c) function foo() {\n\t\t\t// but not “foo {”\n\t\t\t{\n\t\t\t\t// a) and c)\n\t\t\t\tpattern: /(\\bfunction\\s+)[\\w-]+(?=(?:\\s*\\(?:\\s*\\))?\\s*\\{)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\talias: 'function'\n\t\t\t},\n\t\t\t{\n\t\t\t\t// b)\n\t\t\t\tpattern: /\\b[\\w-]+(?=\\s*\\(\\s*\\)\\s*\\{)/,\n\t\t\t\talias: 'function'\n\t\t\t}\n\t\t],\n\t\t// Highlight variable names as variables in for and select beginnings.\n\t\t'for-or-select': {\n\t\t\tpattern: /(\\b(?:for|select)\\s+)\\w+(?=\\s+in\\s)/,\n\t\t\talias: 'variable',\n\t\t\tlookbehind: true\n\t\t},\n\t\t// Highlight variable names as variables in the left-hand part\n\t\t// of assignments (“=” and “+=”).\n\t\t'assign-left': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()\\w+(?:\\.\\w+)*(?=\\+?=)/,\n\t\t\tinside: {\n\t\t\t\t'environment': {\n\t\t\t\t\tpattern: RegExp('(^|[\\\\s;|&]|[<>]\\\\()' + envVars),\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\talias: 'constant'\n\t\t\t\t}\n\t\t\t},\n\t\t\talias: 'variable',\n\t\t\tlookbehind: true\n\t\t},\n\t\t// Highlight parameter names as variables\n\t\t'parameter': {\n\t\t\tpattern: /(^|\\s)-{1,2}(?:\\w+:[+-]?)?\\w+(?:\\.\\w+)*(?=[=\\s]|$)/,\n\t\t\talias: 'variable',\n\t\t\tlookbehind: true\n\t\t},\n\t\t'string': [\n\t\t\t// Support for Here-documents https://en.wikipedia.org/wiki/Here_document\n\t\t\t{\n\t\t\t\tpattern: /((?:^|[^<])<<-?\\s*)(\\w+)\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\2/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: insideString\n\t\t\t},\n\t\t\t// Here-document with quotes around the tag\n\t\t\t// → No expansion (so no “inside”).\n\t\t\t{\n\t\t\t\tpattern: /((?:^|[^<])<<-?\\s*)([\"'])(\\w+)\\2\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\3/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'bash': commandAfterHeredoc\n\t\t\t\t}\n\t\t\t},\n\t\t\t// “Normal” string\n\t\t\t{\n\t\t\t\t// https://www.gnu.org/software/bash/manual/html_node/Double-Quotes.html\n\t\t\t\tpattern: /(^|[^\\\\](?:\\\\\\\\)*)\"(?:\\\\[\\s\\S]|\\$\\([^)]+\\)|\\$(?!\\()|`[^`]+`|[^\"\\\\`$])*\"/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: insideString\n\t\t\t},\n\t\t\t{\n\t\t\t\t// https://www.gnu.org/software/bash/manual/html_node/Single-Quotes.html\n\t\t\t\tpattern: /(^|[^$\\\\])'[^']*'/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\t// https://www.gnu.org/software/bash/manual/html_node/ANSI_002dC-Quoting.html\n\t\t\t\tpattern: /\\$'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'entity': insideString.entity\n\t\t\t\t}\n\t\t\t}\n\t\t],\n\t\t'environment': {\n\t\t\tpattern: RegExp('\\\\$?' + envVars),\n\t\t\talias: 'constant'\n\t\t},\n\t\t'variable': insideString.variable,\n\t\t'function': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()(?:add|apropos|apt|apt-cache|apt-get|aptitude|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cargo|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|docker|docker-compose|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|java|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|node|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|podman|podman-compose|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|sysctl|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vcpkg|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\\s;|&])/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'keyword': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()(?:case|do|done|elif|else|esac|fi|for|function|if|in|select|then|until|while)(?=$|[)\\s;|&])/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t// https://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n\t\t'builtin': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()(?:\\.|:|alias|bind|break|builtin|caller|cd|command|continue|declare|echo|enable|eval|exec|exit|export|getopts|hash|help|let|local|logout|mapfile|printf|pwd|read|readarray|readonly|return|set|shift|shopt|source|test|times|trap|type|typeset|ulimit|umask|unalias|unset)(?=$|[)\\s;|&])/,\n\t\t\tlookbehind: true,\n\t\t\t// Alias added to make those easier to distinguish from strings.\n\t\t\talias: 'class-name'\n\t\t},\n\t\t'boolean': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()(?:false|true)(?=$|[)\\s;|&])/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'file-descriptor': {\n\t\t\tpattern: /\\B&\\d\\b/,\n\t\t\talias: 'important'\n\t\t},\n\t\t'operator': {\n\t\t\t// Lots of redirections here, but not just that.\n\t\t\tpattern: /\\d?<>|>\\||\\+=|=[=~]?|!=?|<<[<-]?|[&\\d]?>>|\\d[<>]&?|[<>][&=]?|&[>&]?|\\|[&|]?/,\n\t\t\tinside: {\n\t\t\t\t'file-descriptor': {\n\t\t\t\t\tpattern: /^\\d/,\n\t\t\t\t\talias: 'important'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t'punctuation': /\\$?\\(\\(?|\\)\\)?|\\.\\.|[{}[\\];\\\\]/,\n\t\t'number': {\n\t\t\tpattern: /(^|\\s)(?:[1-9]\\d*|0)(?:[.,]\\d+)?\\b/,\n\t\t\tlookbehind: true\n\t\t}\n\t};\n\n\tcommandAfterHeredoc.inside = Prism.languages.bash;\n\n\t/* Patterns in command substitution. */\n\tvar toBeCopied = [\n\t\t'comment',\n\t\t'function-name',\n\t\t'for-or-select',\n\t\t'assign-left',\n\t\t'parameter',\n\t\t'string',\n\t\t'environment',\n\t\t'function',\n\t\t'keyword',\n\t\t'builtin',\n\t\t'boolean',\n\t\t'file-descriptor',\n\t\t'operator',\n\t\t'punctuation',\n\t\t'number'\n\t];\n\tvar inside = insideString.variable[1].inside;\n\tfor (var i = 0; i < toBeCopied.length; i++) {\n\t\tinside[toBeCopied[i]] = Prism.languages.bash[toBeCopied[i]];\n\t}\n\n\tPrism.languages.sh = Prism.languages.bash;\n\tPrism.languages.shell = Prism.languages.bash;\n}(Prism));\n"], "mappings": ";CAAC,SAAUA,QAAO;AAKjB,MAAI,UAAU;AAEd,MAAI,sBAAsB;AAAA,IACzB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO;AAAA;AAAA,IACP,QAAQ;AAAA;AAAA,EACT;AAEA,MAAI,eAAe;AAAA,IAClB,QAAQ;AAAA,IACR,eAAe;AAAA,MACd,SAAS,OAAO,QAAQ,OAAO;AAAA,MAC/B,OAAO;AAAA,IACR;AAAA,IACA,YAAY;AAAA;AAAA,MAEX;AAAA,QACC,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA;AAAA,UAEP,YAAY;AAAA,YACX;AAAA,cACC,SAAS;AAAA,cACT,YAAY;AAAA,YACb;AAAA,YACA;AAAA,UACD;AAAA,UACA,UAAU;AAAA;AAAA,UAEV,YAAY;AAAA;AAAA,UAEZ,eAAe;AAAA,QAChB;AAAA,MACD;AAAA;AAAA,MAEA;AAAA,QACC,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,YAAY;AAAA,QACb;AAAA,MACD;AAAA;AAAA,MAEA;AAAA,QACC,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,eAAe;AAAA,YACd,SAAS,OAAO,UAAU,OAAO;AAAA,YACjC,YAAY;AAAA,YACZ,OAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA;AAAA,IAEA,UAAU;AAAA,EACX;AAEA,EAAAA,OAAM,UAAU,OAAO;AAAA,IACtB,WAAW;AAAA,MACV,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,IACA,WAAW;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,IACA,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKhB;AAAA;AAAA,QAEC,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,MACR;AAAA,MACA;AAAA;AAAA,QAEC,SAAS;AAAA,QACT,OAAO;AAAA,MACR;AAAA,IACD;AAAA;AAAA,IAEA,iBAAiB;AAAA,MAChB,SAAS;AAAA,MACT,OAAO;AAAA,MACP,YAAY;AAAA,IACb;AAAA;AAAA;AAAA,IAGA,eAAe;AAAA,MACd,SAAS;AAAA,MACT,QAAQ;AAAA,QACP,eAAe;AAAA,UACd,SAAS,OAAO,yBAAyB,OAAO;AAAA,UAChD,YAAY;AAAA,UACZ,OAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA,OAAO;AAAA,MACP,YAAY;AAAA,IACb;AAAA;AAAA,IAEA,aAAa;AAAA,MACZ,SAAS;AAAA,MACT,OAAO;AAAA,MACP,YAAY;AAAA,IACb;AAAA,IACA,UAAU;AAAA;AAAA,MAET;AAAA,QACC,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AAAA;AAAA;AAAA,MAGA;AAAA,QACC,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,QAAQ;AAAA,QACT;AAAA,MACD;AAAA;AAAA,MAEA;AAAA;AAAA,QAEC,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AAAA,MACA;AAAA;AAAA,QAEC,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,MACA;AAAA;AAAA,QAEC,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,UAAU,aAAa;AAAA,QACxB;AAAA,MACD;AAAA,IACD;AAAA,IACA,eAAe;AAAA,MACd,SAAS,OAAO,SAAS,OAAO;AAAA,MAChC,OAAO;AAAA,IACR;AAAA,IACA,YAAY,aAAa;AAAA,IACzB,YAAY;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,IACA,WAAW;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA;AAAA,IAEA,WAAW;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA;AAAA,MAEZ,OAAO;AAAA,IACR;AAAA,IACA,WAAW;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,IACA,mBAAmB;AAAA,MAClB,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,IACA,YAAY;AAAA;AAAA,MAEX,SAAS;AAAA,MACT,QAAQ;AAAA,QACP,mBAAmB;AAAA,UAClB,SAAS;AAAA,UACT,OAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA,IACA,eAAe;AAAA,IACf,UAAU;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,EACD;AAEA,sBAAoB,SAASA,OAAM,UAAU;AAG7C,MAAI,aAAa;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACA,MAAI,SAAS,aAAa,SAAS,CAAC,EAAE;AACtC,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC3C,WAAO,WAAW,CAAC,CAAC,IAAIA,OAAM,UAAU,KAAK,WAAW,CAAC,CAAC;AAAA,EAC3D;AAEA,EAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AACrC,EAAAA,OAAM,UAAU,QAAQA,OAAM,UAAU;AACzC,GAAE,KAAK;", "names": ["Prism"]}