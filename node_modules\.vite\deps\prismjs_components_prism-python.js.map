{"version": 3, "sources": ["../../prismjs/components/prism-python.js"], "sourcesContent": ["Prism.languages.python = {\n\t'comment': {\n\t\tpattern: /(^|[^\\\\])#.*/,\n\t\tlookbehind: true,\n\t\tgreedy: true\n\t},\n\t'string-interpolation': {\n\t\tpattern: /(?:f|fr|rf)(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2)/i,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'interpolation': {\n\t\t\t\t// \"{\" <expression> <optional \"!s\", \"!r\", or \"!a\"> <optional \":\" format specifier> \"}\"\n\t\t\t\tpattern: /((?:^|[^{])(?:\\{\\{)*)\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}])+\\})+\\})+\\}/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'format-spec': {\n\t\t\t\t\t\tpattern: /(:)[^:(){}]+(?=\\}$)/,\n\t\t\t\t\t\tlookbehind: true\n\t\t\t\t\t},\n\t\t\t\t\t'conversion-option': {\n\t\t\t\t\t\tpattern: /![sra](?=[:}]$)/,\n\t\t\t\t\t\talias: 'punctuation'\n\t\t\t\t\t},\n\t\t\t\t\trest: null\n\t\t\t\t}\n\t\t\t},\n\t\t\t'string': /[\\s\\S]+/\n\t\t}\n\t},\n\t'triple-quoted-string': {\n\t\tpattern: /(?:[rub]|br|rb)?(\"\"\"|''')[\\s\\S]*?\\1/i,\n\t\tgreedy: true,\n\t\talias: 'string'\n\t},\n\t'string': {\n\t\tpattern: /(?:[rub]|br|rb)?(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/i,\n\t\tgreedy: true\n\t},\n\t'function': {\n\t\tpattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/g,\n\t\tlookbehind: true\n\t},\n\t'class-name': {\n\t\tpattern: /(\\bclass\\s+)\\w+/i,\n\t\tlookbehind: true\n\t},\n\t'decorator': {\n\t\tpattern: /(^[\\t ]*)@\\w+(?:\\.\\w+)*/m,\n\t\tlookbehind: true,\n\t\talias: ['annotation', 'punctuation'],\n\t\tinside: {\n\t\t\t'punctuation': /\\./\n\t\t}\n\t},\n\t'keyword': /\\b(?:_(?=\\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\\b/,\n\t'builtin': /\\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\\b/,\n\t'boolean': /\\b(?:False|None|True)\\b/,\n\t'number': /\\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\\b|(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:e[+-]?\\d+(?:_\\d+)*)?j?(?!\\w)/i,\n\t'operator': /[-+%=]=?|!=|:=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n\t'punctuation': /[{}[\\];(),.:]/\n};\n\nPrism.languages.python['string-interpolation'].inside['interpolation'].inside.rest = Prism.languages.python;\n\nPrism.languages.py = Prism.languages.python;\n"], "mappings": ";AAAA,MAAM,UAAU,SAAS;AAAA,EACxB,WAAW;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,EACT;AAAA,EACA,wBAAwB;AAAA,IACvB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACP,iBAAiB;AAAA;AAAA,QAEhB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,UACP,eAAe;AAAA,YACd,SAAS;AAAA,YACT,YAAY;AAAA,UACb;AAAA,UACA,qBAAqB;AAAA,YACpB,SAAS;AAAA,YACT,OAAO;AAAA,UACR;AAAA,UACA,MAAM;AAAA,QACP;AAAA,MACD;AAAA,MACA,UAAU;AAAA,IACX;AAAA,EACD;AAAA,EACA,wBAAwB;AAAA,IACvB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,EACR;AAAA,EACA,UAAU;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACX,SAAS;AAAA,IACT,YAAY;AAAA,EACb;AAAA,EACA,cAAc;AAAA,IACb,SAAS;AAAA,IACT,YAAY;AAAA,EACb;AAAA,EACA,aAAa;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO,CAAC,cAAc,aAAa;AAAA,IACnC,QAAQ;AAAA,MACP,eAAe;AAAA,IAChB;AAAA,EACD;AAAA,EACA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AAChB;AAEA,MAAM,UAAU,OAAO,sBAAsB,EAAE,OAAO,eAAe,EAAE,OAAO,OAAO,MAAM,UAAU;AAErG,MAAM,UAAU,KAAK,MAAM,UAAU;", "names": []}