<template>
  <n-modal
    v-model:show="showModal"
    :mask-closable="false"
    :close-on-esc="false"
    :auto-focus="false"
    transform-origin="center"
    class="settings-modal"
  >
    <div class="settings-window-container">
      <!-- 自定义标题栏 -->
      <div class="settings-titlebar">
        <div class="titlebar-content">
          <div class="titlebar-left">
            <div class="titlebar-icon">
              <i class="i-carbon-settings text-lg" />
            </div>
            <div class="titlebar-title">AI桌面客户端 - 设置</div>
          </div>
          <div class="titlebar-controls">
            <button
              class="titlebar-button close"
              @click="closeModal"
              title="关闭"
            >
              <i class="i-carbon-close" />
            </button>
          </div>
        </div>
      </div>

      <!-- 设置内容 -->
      <div class="settings-content">
        <div class="settings-layout">
          <!-- 顶部标签导航 -->
          <div class="settings-tabs">
            <div
              v-for="tab in settingsTabs"
              :key="tab.key"
              class="tab-item"
              :class="{ active: activeTab === tab.key }"
              @click="activeTab = tab.key"
            >
              <i :class="tab.icon" />
              <span>{{ tab.label }}</span>
            </div>
          </div>

          <!-- 设置内容区域 -->
          <div class="settings-panel">
            <div class="panel-content">
              <!-- 通用设置 -->
              <div v-if="activeTab === 'general'" class="setting-group">
                <div class="p-6">
                  <h3>通用设置</h3>
                  <p>通用设置功能开发中...</p>
                </div>
              </div>

              <!-- AI提供商设置 -->
              <div v-else-if="activeTab === 'providers'" class="setting-group">
                <AIProviderSettings />
              </div>

              <!-- 模型参数设置 -->
              <div v-else-if="activeTab === 'models'" class="setting-group">
                <div class="p-6">
                  <h3>模型参数</h3>
                  <p>模型参数设置功能开发中...</p>
                </div>
              </div>

              <!-- 助手管理 -->
              <div v-else-if="activeTab === 'assistants'" class="setting-group">
                <div class="p-6">
                  <h3>助手管理</h3>
                  <p>助手管理功能开发中...</p>
                </div>
              </div>

              <!-- 高级设置 -->
              <div v-else-if="activeTab === 'advanced'" class="setting-group">
                <div class="p-6">
                  <h3>高级设置</h3>
                  <p>高级设置功能开发中...</p>
                </div>
              </div>

              <!-- 关于 -->
              <div v-else-if="activeTab === 'about'" class="setting-group">
                <div class="p-6">
                  <h3>关于</h3>
                  <p>关于页面功能开发中...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useSettingsStore } from '@/stores/settings'

// 导入设置组件
import AIProviderSettings from './AIProviderSettings.vue'
// import GeneralSettings from './GeneralSettings.vue'
// import ModelSettings from './ModelSettings.vue'
// import AssistantSettings from './AssistantSettings.vue'
// import AdvancedSettings from './AdvancedSettings.vue'
// import AboutSettings from './AboutSettings.vue'

const message = useMessage()
const settingsStore = useSettingsStore()

// 响应式数据
const showModal = ref(false)
const activeTab = ref('providers')

// 设置标签页配置
const settingsTabs = [
  {
    key: 'general',
    label: '通用',
    icon: 'i-carbon-settings',
    description: '应用外观、语言和基本行为设置'
  },
  {
    key: 'providers',
    label: 'AI提供商',
    icon: 'i-carbon-cloud-service-management',
    description: '配置和管理AI服务提供商'
  },
  {
    key: 'models',
    label: '模型参数',
    icon: 'i-carbon-model-alt',
    description: '调整AI模型的参数和行为'
  },
  {
    key: 'assistants',
    label: '助手管理',
    icon: 'i-carbon-user-multiple',
    description: '创建和管理AI助手'
  },
  {
    key: 'advanced',
    label: '高级设置',
    icon: 'i-carbon-settings-adjust',
    description: '高级功能和开发者选项'
  },
  {
    key: 'about',
    label: '关于',
    icon: 'i-carbon-information',
    description: '应用信息和版本详情'
  }
]

// 计算属性
const currentTabInfo = computed(() => settingsTabs.find(tab => tab.key === activeTab.value))

// 方法
const openModal = () => {
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
}



// 监听全局事件
const handleOpenSettings = () => {
  openModal()
}

// 组件挂载和卸载
onMounted(() => {
  window.addEventListener('open-settings-modal', handleOpenSettings)
})

onUnmounted(() => {
  window.removeEventListener('open-settings-modal', handleOpenSettings)
})

// 暴露方法给父组件
defineExpose({
  openModal,
  closeModal
})
</script>

<style scoped>
.settings-modal :deep(.n-modal) {
  max-width: none !important;
  max-height: none !important;
  margin: 0 !important;
}

.settings-window-container {
  width: 900px;
  height: calc(100vh - 120px);
  max-height: 600px;
  background: #f8fafc;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  margin: 60px auto;
}

/* 自定义标题栏 */
.settings-titlebar {
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  user-select: none;
  flex-shrink: 0;
}

.titlebar-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  color: white;
}

.titlebar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.titlebar-icon {
  display: flex;
  align-items: center;
}

.titlebar-title {
  font-size: 14px;
  font-weight: 500;
}

.titlebar-controls {
  display: flex;
  gap: 8px;
}

.titlebar-button {
  width: 28px;
  height: 28px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.titlebar-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.titlebar-button.close:hover {
  background: #ef4444;
}

/* 设置内容区域 */
.settings-content {
  flex: 1;
  overflow: hidden;
  background: #f8fafc;
}

.settings-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 16px;
}

/* 顶部标签导航 */
.settings-tabs {
  display: flex;
  background: white;
  border-radius: 8px;
  padding: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 12px 0;
  overflow-x: auto;
  gap: 3px;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  min-width: fit-content;
}

.tab-item:hover {
  background: #f1f5f9;
  color: #374151;
}

.tab-item.active {
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.tab-item i {
  font-size: 16px;
}

/* 设置面板 */
.settings-panel {
  flex: 1;
  overflow: hidden;
}

.panel-content {
  height: 100%;
  overflow-y: auto;
  padding-bottom: 16px;
}

.setting-group {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}



/* 响应式设计 */
@media (max-width: 1024px) {
  .settings-window-container {
    width: 800px;
    height: calc(100vh - 100px);
    max-height: 500px;
    margin: 50px auto;
  }

  .settings-layout {
    padding: 0 16px;
  }

  .settings-tabs {
    padding: 6px;
    margin: 12px 0;
  }

  .tab-item {
    padding: 10px 16px;
    font-size: 13px;
  }


}

@media (max-width: 768px) {
  .settings-window-container {
    width: calc(100vw - 32px);
    height: calc(100vh - 80px);
    border-radius: 12px;
    margin: 40px auto;
  }

  .settings-layout {
    padding: 0 12px;
  }

  .settings-tabs {
    flex-wrap: wrap;
    gap: 2px;
    padding: 4px;
    margin: 8px 0;
  }

  .tab-item {
    flex: 1;
    min-width: calc(50% - 2px);
    justify-content: center;
    padding: 8px 12px;
    font-size: 12px;
  }
}
</style>
