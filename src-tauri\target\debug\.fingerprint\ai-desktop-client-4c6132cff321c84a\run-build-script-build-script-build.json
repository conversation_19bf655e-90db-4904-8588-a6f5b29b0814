{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11349158298389822207, "build_script_build", false, 3893757781094642492], [12092653563678505622, "build_script_build", false, 5062801886038644262]], "local": [{"RerunIfChanged": {"output": "debug\\build\\ai-desktop-client-4c6132cff321c84a\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}