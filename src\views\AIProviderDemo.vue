<template>
  <div class="ai-provider-demo">
    <div class="demo-header">
      <h1>AI提供商设置演示</h1>
      <p>基于您提供的截图设计的AI提供商配置界面</p>
    </div>

    <div class="demo-content">
      <AIProviderSettings />
    </div>

    <div class="demo-footer">
      <div class="demo-info">
        <h3>功能特点</h3>
        <ul>
          <li>✅ 支持多个AI提供商（DeepSeek、OpenAI、Anthropic、Google）</li>
          <li>✅ 可视化的提供商卡片界面</li>
          <li>✅ 详细的模型配置选项</li>
          <li>✅ API密钥和基础URL配置</li>
          <li>✅ 连接测试功能</li>
          <li>✅ 模型特性标签（流式、视觉、函数）</li>
          <li>✅ 响应式设计，支持移动端</li>
        </ul>
      </div>

      <div class="demo-tech">
        <h3>技术实现</h3>
        <ul>
          <li>Vue 3 + TypeScript</li>
          <li>Naive UI 组件库</li>
          <li>Pinia 状态管理</li>
          <li>组件化设计</li>
          <li>响应式布局</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AIProviderSettings from '@/components/Settings/AIProviderSettings.vue'
</script>

<style scoped>
.ai-provider-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.demo-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.demo-header p {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

.demo-content {
  max-width: 1400px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 600px;
}

.demo-footer {
  max-width: 1400px;
  margin: 40px auto 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  color: white;
}

.demo-info,
.demo-tech {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.demo-info h3,
.demo-tech h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #fff;
}

.demo-info ul,
.demo-tech ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.demo-info li,
.demo-tech li {
  padding: 8px 0;
  font-size: 1rem;
  opacity: 0.9;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.demo-info li:last-child,
.demo-tech li:last-child {
  border-bottom: none;
}

.demo-info li::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #4ade80;
  border-radius: 50%;
  margin-right: 12px;
  vertical-align: middle;
}

.demo-tech li::before {
  content: '▸';
  color: #60a5fa;
  font-weight: bold;
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .demo-footer {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .ai-provider-demo {
    padding: 12px;
  }
  
  .demo-header h1 {
    font-size: 2rem;
  }
  
  .demo-header p {
    font-size: 1rem;
  }
  
  .demo-content {
    border-radius: 12px;
  }
  
  .demo-info,
  .demo-tech {
    padding: 16px;
  }
}

/* 动画效果 */
.demo-content {
  animation: slideUp 0.6s ease-out;
}

.demo-footer > div {
  animation: fadeIn 0.8s ease-out 0.3s both;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
