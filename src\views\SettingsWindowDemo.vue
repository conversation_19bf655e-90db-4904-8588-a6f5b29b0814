<template>
  <div class="settings-window-demo">
    <!-- 演示说明 -->
    <div class="demo-header">
      <h1>🖥️ 专业桌面设置窗口</h1>
      <p>基于Tauri的独立设置窗口，专业的桌面应用体验</p>
      <div class="demo-badges">
        <span class="badge">✨ 独立窗口</span>
        <span class="badge">🎨 专业界面</span>
        <span class="badge">⚡ 高性能</span>
        <span class="badge">📱 响应式</span>
      </div>
    </div>

    <!-- 模拟窗口容器 -->
    <div class="window-container">
      <div class="mock-window">
        <!-- 模拟窗口标题栏 -->
        <div class="mock-titlebar">
          <div class="titlebar-buttons">
            <div class="button close"></div>
            <div class="button minimize"></div>
            <div class="button maximize"></div>
          </div>
          <div class="titlebar-title">AI桌面客户端 - 设置</div>
          <div class="titlebar-spacer"></div>
        </div>

        <!-- 设置窗口内容 -->
        <div class="mock-content">
          <SettingsWindow />
        </div>
      </div>
    </div>

    <!-- 功能特点 -->
    <div class="features-section">
      <h2>🚀 功能特点</h2>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">🪟</div>
          <h3>独立窗口</h3>
          <p>使用Tauri创建的原生桌面窗口，完全独立于主应用</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">🎛️</div>
          <h3>专业界面</h3>
          <p>仿照专业软件设计的设置界面，分类清晰，操作便捷</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">⚙️</div>
          <h3>完整配置</h3>
          <p>涵盖AI提供商、模型参数、助手管理等全方位设置</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">💾</div>
          <h3>实时保存</h3>
          <p>配置更改实时保存，支持导入导出和重置功能</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">🔒</div>
          <h3>安全可靠</h3>
          <p>API密钥安全存储，支持密码显示/隐藏切换</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">📱</div>
          <h3>响应式设计</h3>
          <p>适配不同窗口尺寸，支持最小化和窗口控制</p>
        </div>
      </div>
    </div>

    <!-- 技术实现 -->
    <div class="tech-section">
      <h2>🛠️ 技术实现</h2>
      <div class="tech-grid">
        <div class="tech-item">
          <strong>窗口管理</strong>
          <span>Tauri WebviewWindow API</span>
        </div>
        <div class="tech-item">
          <strong>界面框架</strong>
          <span>Vue 3 + TypeScript</span>
        </div>
        <div class="tech-item">
          <strong>UI组件</strong>
          <span>Naive UI 组件库</span>
        </div>
        <div class="tech-item">
          <strong>状态管理</strong>
          <span>Pinia + 本地存储</span>
        </div>
        <div class="tech-item">
          <strong>样式系统</strong>
          <span>CSS3 + UnoCSS</span>
        </div>
        <div class="tech-item">
          <strong>窗口通信</strong>
          <span>Tauri Event System</span>
        </div>
      </div>
    </div>

    <!-- 使用说明 -->
    <div class="usage-section">
      <h2>📖 使用说明</h2>
      <div class="usage-steps">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>打开设置</h4>
            <p>点击主应用右上角的设置按钮，或使用快捷键 Ctrl+,</p>
          </div>
        </div>
        <div class="step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>配置AI提供商</h4>
            <p>在"AI提供商"标签页中配置API密钥和选择模型</p>
          </div>
        </div>
        <div class="step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>调整参数</h4>
            <p>在"模型参数"中调整温度、Token数等生成参数</p>
          </div>
        </div>
        <div class="step">
          <div class="step-number">4</div>
          <div class="step-content">
            <h4>保存设置</h4>
            <p>点击"保存并关闭"按钮应用所有更改</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SettingsWindow from './SettingsWindow.vue'
</script>

<style scoped>
.settings-window-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.demo-header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.demo-header p {
  font-size: 1.3rem;
  opacity: 0.9;
  margin-bottom: 24px;
}

.demo-badges {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.window-container {
  max-width: 1200px;
  margin: 0 auto 60px;
  perspective: 1000px;
}

.mock-window {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  transform: rotateX(5deg) rotateY(-5deg);
  transition: transform 0.3s ease;
}

.mock-window:hover {
  transform: rotateX(0deg) rotateY(0deg);
}

.mock-titlebar {
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  padding: 0 16px;
  color: white;
}

.titlebar-buttons {
  display: flex;
  gap: 8px;
}

.button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.button.close { background: #ff5f57; }
.button.minimize { background: #ffbd2e; }
.button.maximize { background: #28ca42; }

.titlebar-title {
  flex: 1;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}

.titlebar-spacer {
  width: 60px;
}

.mock-content {
  height: 700px;
  overflow: hidden;
}

.features-section,
.tech-section,
.usage-section {
  max-width: 1200px;
  margin: 0 auto 60px;
  color: white;
}

.features-section h2,
.tech-section h2,
.usage-section h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 40px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.feature-card p {
  opacity: 0.9;
  line-height: 1.6;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.tech-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tech-item strong {
  font-size: 16px;
  font-weight: 600;
}

.tech-item span {
  opacity: 0.8;
  font-size: 14px;
}

.usage-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.step {
  display: flex;
  gap: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.step-number {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
  flex-shrink: 0;
}

.step-content h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.step-content p {
  opacity: 0.9;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-window-demo {
    padding: 20px 12px;
  }
  
  .demo-header h1 {
    font-size: 2rem;
  }
  
  .demo-header p {
    font-size: 1rem;
  }
  
  .mock-window {
    transform: none;
  }
  
  .mock-content {
    height: 500px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .tech-grid {
    grid-template-columns: 1fr;
  }
  
  .usage-steps {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.mock-window {
  animation: slideIn 0.8s ease-out;
}

.feature-card {
  animation: fadeInUp 0.6s ease-out;
}

.feature-card:nth-child(2) { animation-delay: 0.1s; }
.feature-card:nth-child(3) { animation-delay: 0.2s; }
.feature-card:nth-child(4) { animation-delay: 0.3s; }
.feature-card:nth-child(5) { animation-delay: 0.4s; }
.feature-card:nth-child(6) { animation-delay: 0.5s; }

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(50px) rotateX(10deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotateX(5deg) rotateY(-5deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
