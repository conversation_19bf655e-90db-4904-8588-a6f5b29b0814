import { ref, computed, watch, nextTick } from 'vue'

export interface ContentChunk {
  id: string
  content: string
  startIndex: number
  endIndex: number
  isLoaded: boolean
}

export interface ChunkedContentOptions {
  chunkSize?: number
  maxInitialChunks?: number
  loadDelay?: number
}

export function useChunkedContent(
  content: string,
  options: ChunkedContentOptions = {}
) {
  const {
    chunkSize = 1000,
    maxInitialChunks = 3,
    loadDelay = 100
  } = options

  const chunks = ref<ContentChunk[]>([])
  const loadedChunks = ref(new Set<string>())
  const isLoading = ref(false)
  const currentChunkIndex = ref(0)

  // 将内容分割成块
  const createChunks = (text: string): ContentChunk[] => {
    const result: ContentChunk[] = []
    let startIndex = 0

    while (startIndex < text.length) {
      const endIndex = Math.min(startIndex + chunkSize, text.length)
      const chunkContent = text.slice(startIndex, endIndex)
      
      result.push({
        id: `chunk-${result.length}`,
        content: chunkContent,
        startIndex,
        endIndex,
        isLoaded: false
      })

      startIndex = endIndex
    }

    return result
  }

  // 加载指定的块
  const loadChunk = async (chunkId: string) => {
    const chunk = chunks.value.find(c => c.id === chunkId)
    if (!chunk || loadedChunks.value.has(chunkId)) return

    isLoading.value = true
    
    // 模拟异步加载延迟
    await new Promise(resolve => setTimeout(resolve, loadDelay))
    
    chunk.isLoaded = true
    loadedChunks.value.add(chunkId)
    isLoading.value = false

    return chunk
  }

  // 加载下一个块
  const loadNextChunk = async () => {
    if (currentChunkIndex.value >= chunks.value.length) return null

    const chunk = chunks.value[currentChunkIndex.value]
    currentChunkIndex.value++
    
    return await loadChunk(chunk.id)
  }

  // 加载初始块
  const loadInitialChunks = async () => {
    const chunksToLoad = Math.min(maxInitialChunks, chunks.value.length)
    
    for (let i = 0; i < chunksToLoad; i++) {
      await loadChunk(chunks.value[i].id)
    }
  }

  // 已加载的内容
  const loadedContent = computed(() => {
    return chunks.value
      .filter(chunk => chunk.isLoaded)
      .map(chunk => chunk.content)
      .join('')
  })

  // 总进度
  const progress = computed(() => {
    if (chunks.value.length === 0) return 0
    return (loadedChunks.value.size / chunks.value.length) * 100
  })

  // 是否全部加载完成
  const isComplete = computed(() => {
    return chunks.value.length > 0 && loadedChunks.value.size === chunks.value.length
  })

  // 可见的块（已加载的）
  const visibleChunks = computed(() => {
    return chunks.value.filter(chunk => chunk.isLoaded)
  })

  // 重置状态
  const reset = () => {
    chunks.value = []
    loadedChunks.value.clear()
    currentChunkIndex.value = 0
    isLoading.value = false
  }

  // 初始化内容
  const initContent = async (text: string) => {
    reset()
    chunks.value = createChunks(text)
    await nextTick()
    await loadInitialChunks()
  }

  // 监听内容变化
  watch(
    () => content,
    async (newContent) => {
      if (newContent) {
        await initContent(newContent)
      } else {
        reset()
      }
    },
    { immediate: true }
  )

  return {
    chunks: chunks.value,
    loadedChunks,
    loadedContent,
    visibleChunks,
    progress,
    isComplete,
    isLoading,
    loadChunk,
    loadNextChunk,
    loadInitialChunks,
    reset,
    initContent
  }
}
