["\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\E:\\AIAIAI\\src-tauri\\target\\debug\\build\\tauri-f45204cc5ec9435e\\out\\permissions\\menu\\autogenerated\\default.toml"]