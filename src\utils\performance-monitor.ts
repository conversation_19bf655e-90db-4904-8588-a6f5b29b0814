// 性能监控工具
export interface PerformanceMetrics {
  renderTime: number
  memoryUsage: number
  timestamp: number
}

export interface PerformanceConfig {
  enableMonitoring: boolean
  sampleRate: number
  maxSamples: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = []
  private config: PerformanceConfig = {
    enableMonitoring: true,
    sampleRate: 1000, // 每秒采样一次
    maxSamples: 100
  }

  constructor(config?: Partial<PerformanceConfig>) {
    if (config) {
      this.config = { ...this.config, ...config }
    }
  }

  // 记录性能指标
  recordMetrics(renderTime: number) {
    if (!this.config.enableMonitoring) return

    const metrics: PerformanceMetrics = {
      renderTime,
      memoryUsage: this.getMemoryUsage(),
      timestamp: Date.now()
    }

    this.metrics.push(metrics)

    // 保持最大样本数限制
    if (this.metrics.length > this.config.maxSamples) {
      this.metrics.shift()
    }
  }

  // 获取内存使用情况
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024 // MB
    }
    return 0
  }

  // 获取平均渲染时间
  getAverageRenderTime(): number {
    if (this.metrics.length === 0) return 0
    const total = this.metrics.reduce((sum, metric) => sum + metric.renderTime, 0)
    return total / this.metrics.length
  }

  // 获取最新指标
  getLatestMetrics(): PerformanceMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null
  }

  // 获取所有指标
  getAllMetrics(): PerformanceMetrics[] {
    return [...this.metrics]
  }

  // 清除指标
  clearMetrics() {
    this.metrics = []
  }

  // 开始性能测量
  startMeasure(name: string) {
    if (!this.config.enableMonitoring) return
    performance.mark(`${name}-start`)
  }

  // 结束性能测量
  endMeasure(name: string): number {
    if (!this.config.enableMonitoring) return 0
    
    performance.mark(`${name}-end`)
    performance.measure(name, `${name}-start`, `${name}-end`)
    
    const measure = performance.getEntriesByName(name, 'measure')[0]
    const duration = measure ? measure.duration : 0
    
    // 清理标记
    performance.clearMarks(`${name}-start`)
    performance.clearMarks(`${name}-end`)
    performance.clearMeasures(name)
    
    return duration
  }

  // 测量函数执行时间
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    this.startMeasure(name)
    try {
      const result = await fn()
      const duration = this.endMeasure(name)
      this.recordMetrics(duration)
      return result
    } catch (error) {
      this.endMeasure(name)
      throw error
    }
  }

  // 测量同步函数执行时间
  measure<T>(name: string, fn: () => T): T {
    this.startMeasure(name)
    try {
      const result = fn()
      const duration = this.endMeasure(name)
      this.recordMetrics(duration)
      return result
    } catch (error) {
      this.endMeasure(name)
      throw error
    }
  }

  // 更新配置
  updateConfig(config: Partial<PerformanceConfig>) {
    this.config = { ...this.config, ...config }
  }

  // 获取配置
  getConfig(): PerformanceConfig {
    return { ...this.config }
  }
}

// 导出单例实例
export const perfMonitor = new PerformanceMonitor()

// 导出类以便创建新实例
export { PerformanceMonitor }
