<template>
  <div class="high-performance-message-list" ref="containerRef">
    <n-scrollbar ref="scrollbarRef" class="message-scrollbar">
      <div class="message-container" :style="{ paddingBottom: '20px' }">
        <!-- 虚拟滚动消息列表 -->
        <div
          v-for="message in visibleMessages"
          :key="message.id"
          class="message-wrapper"
        >
          <HighPerformanceMessageItem
            :message="message"
            :streaming="streamingMessageId === message.id"
            @regenerate="$emit('regenerate', message.id)"
          />
        </div>
        
        <!-- 加载指示器 -->
        <div v-if="isLoading" class="loading-indicator">
          <n-spin size="small" />
          <span>正在生成回复...</span>
        </div>
      </div>
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { perfMonitor } from '@/utils/performance-monitor'
import HighPerformanceMessageItem from './HighPerformanceMessageItem.vue'
import type { Message } from '@/types'

interface Props {
  messages: Message[]
  streamingMessageId?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  scroll: [event: Event]
  reachTop: []
  reachBottom: []
  regenerate: [messageId: string]
}>()

const containerRef = ref<HTMLElement>()
const scrollbarRef = ref()
const isLoading = ref(false)

// 可见消息列表（简化版虚拟滚动）
const visibleMessages = computed(() => {
  // 这里可以实现更复杂的虚拟滚动逻辑
  // 目前先返回所有消息
  return props.messages
})

// 滚动到底部
const scrollToBottom = async (smooth = true) => {
  await nextTick()
  if (scrollbarRef.value) {
    scrollbarRef.value.scrollTo({
      top: scrollbarRef.value.scrollHeight,
      behavior: smooth ? 'smooth' : 'auto'
    })
  }
}

// 滚动到顶部
const scrollToTop = async (smooth = true) => {
  await nextTick()
  if (scrollbarRef.value) {
    scrollbarRef.value.scrollTo({
      top: 0,
      behavior: smooth ? 'smooth' : 'auto'
    })
  }
}

// 处理滚动事件
const handleScroll = (event: Event) => {
  emit('scroll', event)
  
  const target = event.target as HTMLElement
  if (!target) return
  
  // 检查是否到达顶部
  if (target.scrollTop === 0) {
    emit('reachTop')
  }
  
  // 检查是否到达底部
  if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
    emit('reachBottom')
  }
}

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages.length,
  async (newLength, oldLength) => {
    if (newLength > oldLength) {
      await nextTick()
      scrollToBottom()
    }
  }
)

// 监听流式消息，保持在底部
watch(
  () => props.streamingMessageId,
  async (newId) => {
    if (newId) {
      await nextTick()
      scrollToBottom(false) // 流式消息时不使用平滑滚动
    }
  }
)

// 性能监控
const measureRender = () => {
  perfMonitor.startMeasure('message-list-render')
}

const endMeasureRender = () => {
  const duration = perfMonitor.endMeasure('message-list-render')
  if (duration > 16) { // 超过一帧时间
    console.warn(`Message list render took ${duration.toFixed(2)}ms`)
  }
}

onMounted(() => {
  measureRender()
  nextTick(() => {
    endMeasureRender()
    scrollToBottom(false)
  })
})

onUnmounted(() => {
  perfMonitor.clearMetrics()
})

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
  scrollToTop,
  scrollbarRef
})
</script>

<style scoped>
.high-performance-message-list {
  height: 100%;
  width: 100%;
  position: relative;
}

.message-scrollbar {
  height: 100%;
}

.message-container {
  padding: 16px;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.message-wrapper {
  margin-bottom: 16px;
}

.message-wrapper:last-child {
  margin-bottom: 0;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  color: #666;
  font-size: 14px;
}

/* 优化滚动性能 */
.message-scrollbar :deep(.n-scrollbar-content) {
  will-change: transform;
}

/* 减少重绘 */
.message-wrapper {
  contain: layout style paint;
}
</style>
