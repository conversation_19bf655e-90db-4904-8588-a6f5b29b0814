{"version": 3, "sources": ["../../marked-highlight/src/index.js"], "sourcesContent": ["export function markedHighlight(options) {\n  if (typeof options === 'function') {\n    options = {\n      highlight: options,\n    };\n  }\n\n  if (!options || typeof options.highlight !== 'function') {\n    throw new Error('Must provide highlight function');\n  }\n\n  if (typeof options.langPrefix !== 'string') {\n    options.langPrefix = 'language-';\n  }\n\n  if (typeof options.emptyLangClass !== 'string') {\n    options.emptyLangClass = '';\n  }\n\n  return {\n    async: !!options.async,\n    walkTokens(token) {\n      if (token.type !== 'code') {\n        return;\n      }\n\n      const lang = getLang(token.lang);\n\n      if (options.async) {\n        return Promise.resolve(options.highlight(token.text, lang, token.lang || '')).then(updateToken(token));\n      }\n\n      const code = options.highlight(token.text, lang, token.lang || '');\n      if (code instanceof Promise) {\n        throw new Error('markedHighlight is not set to async but the highlight function is async. Set the async option to true on markedHighlight to await the async highlight function.');\n      }\n      updateToken(token)(code);\n    },\n    useNewRenderer: true,\n    renderer: {\n      code(code, infoString, escaped) {\n        // istanbul ignore next\n        if (typeof code === 'object') {\n          escaped = code.escaped;\n          infoString = code.lang;\n          code = code.text;\n        }\n        const lang = getLang(infoString);\n        const classValue = lang ? options.langPrefix + escape(lang) : options.emptyLangClass;\n        const classAttr = classValue\n          ? ` class=\"${classValue}\"`\n          : '';\n        code = code.replace(/\\n$/, '');\n        return `<pre><code${classAttr}>${escaped ? code : escape(code, true)}\\n</code></pre>`;\n      },\n    },\n  };\n}\n\nfunction getLang(lang) {\n  return (lang || '').match(/\\S*/)[0];\n}\n\nfunction updateToken(token) {\n  return (code) => {\n    if (typeof code === 'string' && code !== token.text) {\n      token.escaped = true;\n      token.text = code;\n    }\n  };\n}\n\n// copied from marked helpers\nconst escapeTest = /[&<>\"']/;\nconst escapeReplace = new RegExp(escapeTest.source, 'g');\nconst escapeTestNoEncode = /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/;\nconst escapeReplaceNoEncode = new RegExp(escapeTestNoEncode.source, 'g');\nconst escapeReplacements = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nfunction escape(html, encode) {\n  if (encode) {\n    if (escapeTest.test(html)) {\n      return html.replace(escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (escapeTestNoEncode.test(html)) {\n      return html.replace(escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n\n  return html;\n}\n"], "mappings": ";;;AAAO,SAAS,gBAAgB,SAAS;AACvC,MAAI,OAAO,YAAY,YAAY;AACjC,cAAU;AAAA,MACR,WAAW;AAAA,IACb;AAAA,EACF;AAEA,MAAI,CAAC,WAAW,OAAO,QAAQ,cAAc,YAAY;AACvD,UAAM,IAAI,MAAM,iCAAiC;AAAA,EACnD;AAEA,MAAI,OAAO,QAAQ,eAAe,UAAU;AAC1C,YAAQ,aAAa;AAAA,EACvB;AAEA,MAAI,OAAO,QAAQ,mBAAmB,UAAU;AAC9C,YAAQ,iBAAiB;AAAA,EAC3B;AAEA,SAAO;AAAA,IACL,OAAO,CAAC,CAAC,QAAQ;AAAA,IACjB,WAAW,OAAO;AAChB,UAAI,MAAM,SAAS,QAAQ;AACzB;AAAA,MACF;AAEA,YAAM,OAAO,QAAQ,MAAM,IAAI;AAE/B,UAAI,QAAQ,OAAO;AACjB,eAAO,QAAQ,QAAQ,QAAQ,UAAU,MAAM,MAAM,MAAM,MAAM,QAAQ,EAAE,CAAC,EAAE,KAAK,YAAY,KAAK,CAAC;AAAA,MACvG;AAEA,YAAM,OAAO,QAAQ,UAAU,MAAM,MAAM,MAAM,MAAM,QAAQ,EAAE;AACjE,UAAI,gBAAgB,SAAS;AAC3B,cAAM,IAAI,MAAM,iKAAiK;AAAA,MACnL;AACA,kBAAY,KAAK,EAAE,IAAI;AAAA,IACzB;AAAA,IACA,gBAAgB;AAAA,IAChB,UAAU;AAAA,MACR,KAAK,MAAM,YAAY,SAAS;AAE9B,YAAI,OAAO,SAAS,UAAU;AAC5B,oBAAU,KAAK;AACf,uBAAa,KAAK;AAClB,iBAAO,KAAK;AAAA,QACd;AACA,cAAM,OAAO,QAAQ,UAAU;AAC/B,cAAM,aAAa,OAAO,QAAQ,aAAa,OAAO,IAAI,IAAI,QAAQ;AACtE,cAAM,YAAY,aACd,WAAW,UAAU,MACrB;AACJ,eAAO,KAAK,QAAQ,OAAO,EAAE;AAC7B,eAAO,aAAa,SAAS,IAAI,UAAU,OAAO,OAAO,MAAM,IAAI,CAAC;AAAA;AAAA,MACtE;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,QAAQ,MAAM;AACrB,UAAQ,QAAQ,IAAI,MAAM,KAAK,EAAE,CAAC;AACpC;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,CAAC,SAAS;AACf,QAAI,OAAO,SAAS,YAAY,SAAS,MAAM,MAAM;AACnD,YAAM,UAAU;AAChB,YAAM,OAAO;AAAA,IACf;AAAA,EACF;AACF;AAGA,IAAM,aAAa;AACnB,IAAM,gBAAgB,IAAI,OAAO,WAAW,QAAQ,GAAG;AACvD,IAAM,qBAAqB;AAC3B,IAAM,wBAAwB,IAAI,OAAO,mBAAmB,QAAQ,GAAG;AACvE,IAAM,qBAAqB;AAAA,EACzB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AACA,IAAM,uBAAuB,CAAC,OAAO,mBAAmB,EAAE;AAC1D,SAAS,OAAO,MAAM,QAAQ;AAC5B,MAAI,QAAQ;AACV,QAAI,WAAW,KAAK,IAAI,GAAG;AACzB,aAAO,KAAK,QAAQ,eAAe,oBAAoB;AAAA,IACzD;AAAA,EACF,OAAO;AACL,QAAI,mBAAmB,KAAK,IAAI,GAAG;AACjC,aAAO,KAAK,QAAQ,uBAAuB,oBAAoB;AAAA,IACjE;AAAA,EACF;AAEA,SAAO;AACT;", "names": []}