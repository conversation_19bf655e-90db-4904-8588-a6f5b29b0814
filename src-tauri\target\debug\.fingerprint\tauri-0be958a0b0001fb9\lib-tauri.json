{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 17160455492222233904, "deps": [[40386456601120721, "percent_encoding", false, 4129156245155979692], [654232091421095663, "tauri_utils", false, 15246828877059152779], [1200537532907108615, "url<PERSON><PERSON>n", false, 4182322478639608682], [2013030631243296465, "webview2_com", false, 11257737392710050888], [3150220818285335163, "url", false, 14879515239404989843], [3331586631144870129, "getrandom", false, 5473704362755020652], [4143744114649553716, "raw_window_handle", false, 14178324387943116473], [4494683389616423722, "muda", false, 7053745231355258314], [4919829919303820331, "serialize_to_javascript", false, 1712959375039458107], [5986029879202738730, "log", false, 5047229584628730050], [8569119365930580996, "serde_json", false, 5199826535354481606], [9010263965687315507, "http", false, 15629939712500996649], [9689903380558560274, "serde", false, 14326913226427773042], [10229185211513642314, "mime", false, 12149313404929737718], [10806645703491011684, "thiserror", false, 6923350312884292379], [11989259058781683633, "dunce", false, 11426863789755588728], [12092653563678505622, "build_script_build", false, 5062801886038644262], [12304025191202589669, "tauri_runtime_wry", false, 7493435680197937393], [12565293087094287914, "window_vibrancy", false, 8434757324924704426], [12943761728066819757, "tauri_runtime", false, 14108504791936398176], [12944427623413450645, "tokio", false, 6182291034215686533], [12986574360607194341, "serde_repr", false, 5766458852573111782], [13077543566650298139, "heck", false, 12227043807642275620], [13405681745520956630, "tauri_macros", false, 16878860036449107983], [13625485746686963219, "anyhow", false, 16521415306001099308], [14585479307175734061, "windows", false, 13749503539899060250], [16928111194414003569, "dirs", false, 13034646352573047406], [17155886227862585100, "glob", false, 16666287207728800018]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-0be958a0b0001fb9\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}